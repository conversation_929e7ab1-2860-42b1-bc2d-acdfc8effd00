/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | "Pacific/Midway"
  | "Pacific/Niue"
  | "Pacific/Honolulu"
  | "Pacific/Rarotonga"
  | "America/Anchorage"
  | "Pacific/Gambier"
  | "America/Los_Angeles"
  | "America/Tijuana"
  | "America/Denver"
  | "America/Phoenix"
  | "America/Chicago"
  | "America/Guatemala"
  | "America/New_York"
  | "America/Bogota"
  | "America/Caracas"
  | "America/Santiago"
  | "America/Buenos_Aires"
  | "America/Sao_Paulo"
  | "Atlantic/South_Georgia"
  | "Atlantic/Azores"
  | "Atlantic/Cape_Verde"
  | "Europe/London"
  | "Europe/Berlin"
  | "Africa/Lagos"
  | "Europe/Athens"
  | "Africa/Cairo"
  | "Europe/Moscow"
  | "Asia/Riyadh"
  | "Asia/Dubai"
  | "Asia/Baku"
  | "Asia/Karachi"
  | "Asia/Tashkent"
  | "Asia/Calcutta"
  | "Asia/Dhaka"
  | "Asia/Almaty"
  | "Asia/Jakarta"
  | "Asia/Bangkok"
  | "Asia/Shanghai"
  | "Asia/Singapore"
  | "Asia/Tokyo"
  | "Asia/Seoul"
  | "Australia/Brisbane"
  | "Australia/Sydney"
  | "Pacific/Guam"
  | "Pacific/Noumea"
  | "Pacific/Auckland"
  | "Pacific/Fiji"

export interface Config {
  auth: {
    users: UserAuthOperations
  }
  blocks: {}
  collections: {
    users: User
    media: Media
    categories: Category
    products: Product
    tags: Tag
    tenants: Tenant
    orders: Order
    reviews: Review
    "payload-locked-documents": PayloadLockedDocument
    "payload-preferences": PayloadPreference
    "payload-migrations": PayloadMigration
  }
  collectionsJoins: {
    categories: {
      subcategories: "categories"
    }
  }
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>
    media: MediaSelect<false> | MediaSelect<true>
    categories: CategoriesSelect<false> | CategoriesSelect<true>
    products: ProductsSelect<false> | ProductsSelect<true>
    tags: TagsSelect<false> | TagsSelect<true>
    tenants: TenantsSelect<false> | TenantsSelect<true>
    orders: OrdersSelect<false> | OrdersSelect<true>
    reviews: ReviewsSelect<false> | ReviewsSelect<true>
    "payload-locked-documents":
      | PayloadLockedDocumentsSelect<false>
      | PayloadLockedDocumentsSelect<true>
    "payload-preferences":
      | PayloadPreferencesSelect<false>
      | PayloadPreferencesSelect<true>
    "payload-migrations":
      | PayloadMigrationsSelect<false>
      | PayloadMigrationsSelect<true>
  }
  db: {
    defaultIDType: string
  }
  globals: {}
  globalsSelect: {}
  locale: null
  user: User & {
    collection: "users"
  }
  jobs: {
    tasks: unknown
    workflows: unknown
  }
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string
    password: string
  }
  login: {
    email: string
    password: string
  }
  registerFirstUser: {
    email: string
    password: string
  }
  unlock: {
    email: string
    password: string
  }
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string
  username: string
  roles?: ("super-admin" | "user")[] | null
  tenants?:
    | {
        tenant: string | Tenant
        id?: string | null
      }[]
    | null
  updatedAt: string
  createdAt: string
  email: string
  resetPasswordToken?: string | null
  resetPasswordExpiration?: string | null
  salt?: string | null
  hash?: string | null
  loginAttempts?: number | null
  lockUntil?: string | null
  password?: string | null
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tenants".
 */
export interface Tenant {
  id: string
  /**
   * This is the name of the store (e.g. Antonio's Store)
   */
  name: string
  /**
   * This is the subdomain for the store (e.g. [slug].funroad.com)
   */
  slug: string
  image?: (string | null) | Media
  /**
   * Stripe Account ID associated with your shop
   */
  stripeAccountId: string
  /**
   * You cannot create products until you submit your Stripe details
   */
  stripeDetailsSubmitted?: boolean | null
  updatedAt: string
  createdAt: string
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string
  tenant?: (string | null) | Tenant
  alt: string
  updatedAt: string
  createdAt: string
  url?: string | null
  thumbnailURL?: string | null
  filename?: string | null
  mimeType?: string | null
  filesize?: number | null
  width?: number | null
  height?: number | null
  focalX?: number | null
  focalY?: number | null
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories".
 */
export interface Category {
  id: string
  name: string
  slug: string
  color?: string | null
  parent?: (string | null) | Category
  subcategories?: {
    docs?: (string | Category)[]
    hasNextPage?: boolean
    totalDocs?: number
  }
  updatedAt: string
  createdAt: string
}
/**
 * You must verify your account before creating products
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products".
 */
export interface Product {
  id: string
  tenant?: (string | null) | Tenant
  name: string
  description?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ("ltr" | "rtl") | null
      format: "left" | "start" | "center" | "right" | "end" | "justify" | ""
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  /**
   * Price in USD
   */
  price: number
  category?: (string | null) | Category
  tags?: (string | Tag)[] | null
  image?: (string | null) | Media
  cover?: (string | null) | Media
  refundPolicy?:
    | ("30-day" | "14-day" | "7-day" | "3-day" | "1-day" | "no-refunds")
    | null
  /**
   * Protected content only visible to customers after purchase. Add product documentation, downloadable files, getting started guides, and bonus materials. Supports Markdown formatting
   */
  content?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ("ltr" | "rtl") | null
      format: "left" | "start" | "center" | "right" | "end" | "justify" | ""
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  /**
   * If checked, this product will not be shown on the public storefront
   */
  isPrivate?: boolean | null
  /**
   * If checked, this product will be archived
   */
  isArchived?: boolean | null
  updatedAt: string
  createdAt: string
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags".
 */
export interface Tag {
  id: string
  name: string
  products?: (string | Product)[] | null
  updatedAt: string
  createdAt: string
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders".
 */
export interface Order {
  id: string
  name: string
  user: string | User
  product: string | Product
  /**
   * Stripe checkout session associated with the order
   */
  stripeCheckoutSessionId: string
  /**
   * Stripe account associated with the order
   */
  stripeAccountId?: string | null
  updatedAt: string
  createdAt: string
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "reviews".
 */
export interface Review {
  id: string
  description: string
  rating: number
  product: string | Product
  user: string | User
  updatedAt: string
  createdAt: string
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string
  document?:
    | ({
        relationTo: "users"
        value: string | User
      } | null)
    | ({
        relationTo: "media"
        value: string | Media
      } | null)
    | ({
        relationTo: "categories"
        value: string | Category
      } | null)
    | ({
        relationTo: "products"
        value: string | Product
      } | null)
    | ({
        relationTo: "tags"
        value: string | Tag
      } | null)
    | ({
        relationTo: "tenants"
        value: string | Tenant
      } | null)
    | ({
        relationTo: "orders"
        value: string | Order
      } | null)
    | ({
        relationTo: "reviews"
        value: string | Review
      } | null)
  globalSlug?: string | null
  user: {
    relationTo: "users"
    value: string | User
  }
  updatedAt: string
  createdAt: string
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string
  user: {
    relationTo: "users"
    value: string | User
  }
  key?: string | null
  value?:
    | {
        [k: string]: unknown
      }
    | unknown[]
    | string
    | number
    | boolean
    | null
  updatedAt: string
  createdAt: string
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string
  name?: string | null
  batch?: number | null
  updatedAt: string
  createdAt: string
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  username?: T
  roles?: T
  tenants?:
    | T
    | {
        tenant?: T
        id?: T
      }
  updatedAt?: T
  createdAt?: T
  email?: T
  resetPasswordToken?: T
  resetPasswordExpiration?: T
  salt?: T
  hash?: T
  loginAttempts?: T
  lockUntil?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  tenant?: T
  alt?: T
  updatedAt?: T
  createdAt?: T
  url?: T
  thumbnailURL?: T
  filename?: T
  mimeType?: T
  filesize?: T
  width?: T
  height?: T
  focalX?: T
  focalY?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories_select".
 */
export interface CategoriesSelect<T extends boolean = true> {
  name?: T
  slug?: T
  color?: T
  parent?: T
  subcategories?: T
  updatedAt?: T
  createdAt?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products_select".
 */
export interface ProductsSelect<T extends boolean = true> {
  tenant?: T
  name?: T
  description?: T
  price?: T
  category?: T
  tags?: T
  image?: T
  cover?: T
  refundPolicy?: T
  content?: T
  isPrivate?: T
  isArchived?: T
  updatedAt?: T
  createdAt?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags_select".
 */
export interface TagsSelect<T extends boolean = true> {
  name?: T
  products?: T
  updatedAt?: T
  createdAt?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tenants_select".
 */
export interface TenantsSelect<T extends boolean = true> {
  name?: T
  slug?: T
  image?: T
  stripeAccountId?: T
  stripeDetailsSubmitted?: T
  updatedAt?: T
  createdAt?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "orders_select".
 */
export interface OrdersSelect<T extends boolean = true> {
  name?: T
  user?: T
  product?: T
  stripeCheckoutSessionId?: T
  stripeAccountId?: T
  updatedAt?: T
  createdAt?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "reviews_select".
 */
export interface ReviewsSelect<T extends boolean = true> {
  description?: T
  rating?: T
  product?: T
  user?: T
  updatedAt?: T
  createdAt?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T
  globalSlug?: T
  user?: T
  updatedAt?: T
  createdAt?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T
  key?: T
  value?: T
  updatedAt?: T
  createdAt?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T
  batch?: T
  updatedAt?: T
  createdAt?: T
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown
}

declare module "payload" {
  export interface GeneratedTypes extends Config {}
}
