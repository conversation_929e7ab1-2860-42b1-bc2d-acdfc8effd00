import config from "./payload.config.js"
import { getPayload } from "payload"

const categories = [
  {
    name: "All",
    slug: "all"
  },
  {
    name: "Business & Money",
    color: "#FFB347",
    slug: "business-money",
    subcategories: [
      { name: "Accounting", slug: "accounting" },
      { name: "Entrepreneurship", slug: "entrepreneurship" },
      { name: "Gigs & Side Projects", slug: "gigs-side-projects" },
      { name: "Investing", slug: "investing" },
      { name: "Management & Leadership", slug: "management-leadership" },
      { name: "Marketing & Sales", slug: "marketing-sales" },
      { name: "Networking, Careers & Jobs", slug: "networking-careers-jobs" },
      { name: "Personal Finance", slug: "personal-finance" },
      { name: "Real Estate", slug: "real-estate" }
    ]
  },
  {
    name: "Software Development",
    color: "#7EC8E3",
    slug: "software-development",
    subcategories: [
      { name: "Web Development", slug: "web-development" },
      { name: "Mobile Development", slug: "mobile-development" },
      { name: "Game Development", slug: "game-development" },
      { name: "Programming Languages", slug: "programming-languages" },
      { name: "DevOps", slug: "devops" }
    ]
  },
  {
    name: "Writing & Publishing",
    color: "#D8B5FF",
    slug: "writing-publishing",
    subcategories: [
      { name: "Fiction", slug: "fiction" },
      { name: "Non-Fiction", slug: "non-fiction" },
      { name: "Blogging", slug: "blogging" },
      { name: "Copywriting", slug: "copywriting" },
      { name: "Self-Publishing", slug: "self-publishing" }
    ]
  },
  {
    name: "Other",
    slug: "other"
  },
  {
    name: "Education",
    color: "#FFE066",
    slug: "education",
    subcategories: [
      { name: "Online Courses", slug: "online-courses" },
      { name: "Tutoring", slug: "tutoring" },
      { name: "Test Preparation", slug: "test-preparation" },
      { name: "Language Learning", slug: "language-learning" }
    ]
  }
]

const seed = async () => {
  try {
    const payload = await getPayload({ config })

    console.log("Creating admin user...")
    await payload.create({
      collection: "users",
      data: {
        email: "<EMAIL>",
        password: "demo",
        username: "admin"
      }
    })

    console.log("Creating categories...")
    for (const category of categories) {
      const parentCategory = await payload.create({
        collection: "categories",
        data: {
          name: category.name,
          slug: category.slug,
          color: category.color,
          parent: null
        }
      })

      console.log(`Created category: ${category.name}`)

      for (const subCategory of category.subcategories || []) {
        await payload.create({
          collection: "categories",
          data: {
            name: subCategory.name,
            slug: subCategory.slug,
            parent: parentCategory.id
          }
        })
        console.log(`  Created subcategory: ${subCategory.name}`)
      }
    }

    console.log("Seeding completed successfully")
    process.exit(0)
  } catch (error) {
    console.error("Error during seeding:", error)
    process.exit(1)
  }
}

seed()
