import type { CollectionConfig, CollectionSlug } from "payload";

export const Tenants: CollectionConfig = {
    slug: "tenants" as CollectionSlug,
    fields: [
        {
            name: "name",
            type: "text",
            required: true,
            label: "Store Name",
            admin: {
                description: "This is the name of the store (e.g. Antonio's Store)"
            }
        },
        {
            name: "slug",
            type: "text",
            required: true,
            unique: true,
            index: true,
            label: "Subdomain",
            admin: {
                description: "This is the subdomain for the store (e.g. [slug].funroad.com)"
            }
        },
        {
            name: "image",
            type: "relationship",
            relationTo: "media" as CollectionSlug,
            hasMany: false,
        },
        {
            name: "stripeAccountId",
            type: "text",
            required: true,
            label: "Stripe Account ID",
            admin: {
                description: "Stripe Account ID associated with your shop"
            }
        },
        {
            name: "stripeDetailsSubmitted",
            type: "checkbox",
            defaultValue: false,
            label: "Stripe Details Submitted",
            admin: {
                description: "You cannot create products until you submit your Stripe details"
            }
        },
    ],
};
