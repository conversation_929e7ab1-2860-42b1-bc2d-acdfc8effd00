@import "tailwindcss";
@import "tw-animate-css";

@plugin "@tailwindcss/typography";

@custom-variant dark (&&:where([data-theme=dark], [data-theme=dark] *));

:root {
  --radius: 0.625rem;

  --theme-color-1: hsl(271 94% 65%);
  --theme-color-2: hsl(178 100% 73%);
  --theme-color-3: hsl(226 92% 77%);

  --background: oklch(1 0 0);
  /* --foreground: oklch(0.145 0 0); */
  --card: oklch(1 0 0);
  /* --card-foreground: oklch(0.145 0 0); */
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  --primary: oklch(0.205 0 0);
  /* --primary-foreground: oklch(0.985 0 0); */
  --secondary: oklch(0.97 0 0);
  /* --secondary-foreground: oklch(0.205 0 0); */

  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);

  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0 0 0);
  --input: oklch(0 0 0);
  --ring: oklch(0.708 0 0);

  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

[data-theme="dark"] {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);

  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);

  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);

  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);

  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);

  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* Tailwind theme variables */
@theme inline {
  --font-main: var(--font-dm-sans);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 4px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --animate-sweep: sweep 1.5s infinite cubic-bezier(0.2, 0.8, 0.8, 0.2);
  --animate-expand: expand 1.5s infinite linear;

  @keyframes sweep {
    0% {
      background-position: -300% 0;
    }
    100% {
      background-position: 300% 0;
    }
  }

  @keyframes expand {
    0% {
      background-size: 0% 0%;
    }
    100% {
      background-size: 300% 300%;
    }
  }
}

/* Base styles */
@layer base {
  * {
    @apply scrollbar-styled border-border outline-ring/50;

    transition: all 0.2s ease;
    caret-color: var(--primary);

    &::selection {
      @apply bg-primary/25;
    }

    &::before,
    &::after {
      transition: all 0.2s ease;
    }
  }

  html {
    @apply scrollbar-hidden scroll-smooth;

    @media (prefers-color-scheme: dark) {
      & {
        color-scheme: light dark;
      }
    }
  }

  body {
    @apply w-[100dvw] overflow-x-hidden;
    @apply bg-background font-main text-foreground;

    & > header,
    & > main,
    & > footer {
      @apply flex center;
    }

    header {
      @apply fixed inset-0 z-10 mx-auto w-full flex-row;

      nav {
        @apply font-main;
      }
    }

    main {
      @apply min-h-[100svh] w-full flex-col;

      /* section,
      article {
        @apply relative flex min-h-screen w-full flex-col center;
      } */
    }

    footer {
      @apply static z-10 mt-36 h-16 w-full flex-row bg-card text-card-foreground;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      @apply font-main text-inherit;
    }
  }
}

/* Custom classes with lower specificity (easier to override) */
@layer components {
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }

  button:disabled,
  [role="button"]:disabled {
    cursor: not-allowed;
  }

  [data-container] {
    @apply container flex size-full flex-row justify-between;
  }

  [data-background] {
    @apply pointer-events-none isolate;

    &::before,
    &::after {
      content: "";
      position: absolute;
      inset: 0;
    }

    &::before {
      background-image: url("/images/crystal.svg");
      background-repeat: repeat;
      background-position: center;
      background-size: 20%;

      filter: opacity(0.15) brightness(0.7) invert(1);
    }

    &::after {
      background-color: color-mix(in hsl, var(--theme-color-3) 25%, transparent);
    }
  }
}

/* Custom utility classes */
@utility container {
  & {
    @apply center self-center-safe;
  }
}

@utility scrollbar-hidden {
  & {
    scrollbar-width: none;
  }

  &::-webkit-scrollbar {
    display: none;
  }
}

@utility scrollbar-styled {
  & {
    scrollbar-width: thin !important;
    scrollbar-color: var(--primary) transparent !important;
  }

  &::-webkit-scrollbar {
    @apply !h-[0.6rem] !w-[0.6rem];
  }

  &::-webkit-scrollbar-track {
    @apply !rounded-[--radius] !bg-transparent;
  }

  &::-webkit-scrollbar-thumb {
    @apply !rounded-[--radius] !bg-primary hover:!bg-secondary;
  }
}

@utility center {
  & {
    @apply place-content-center-safe place-items-center-safe;
  }
}

@utility absolute-center {
  & {
    @apply absolute;
  }

  &:not([data-axis]) {
    @apply inset-0 m-auto;
  }

  &[data-axis="x"] {
    @apply inset-x-0 mx-auto;
  }

  &[data-axis="y"] {
    @apply inset-y-0 my-auto;
  }
}

@utility glass-morph {
  & {
    @apply bg-background/50 bg-blend-overlay;
    /* url("/noise.svg") center/cover; */

  /*   backdrop-filter: blur(5px) saturate(200%) sepia(10%) brightness(150%);
    will-change: backdrop-filter, transform;
 */
    /* &::after {
      content: "";
      position: absolute;
      inset: 0;
      z-index: -1;
      filter: url("#noise-filter") opacity(0.5);
    } */
  }
}

@utility eclipse-mask {
  & {
    position: absolute;
    inset: 0;
    margin: auto;
    pointer-events: none;

    background-clip: padding-box;
    background-size: cover;

    background-image: radial-gradient(
      circle at center,
      transparent 50%,
      var(--background) 90%
    );

    filter: blur(10px);
  }
}

@utility bg-grid {
  & {
    position: absolute;
    inset: 0;

    background-size: 20px 20px;
    background-image: linear-gradient(
        to right,
        var(--muted) 1px,
        transparent 1px
      ), linear-gradient(to bottom, var(--muted) 1px, transparent 1px);
  }

  &::after {
    content: "";
    pointer-events: none;
    position: absolute;
    inset: 0;

    display: flex;
    align-items: center;
    justify-content: center;

    background-color: var(--background);
    mask-image: radial-gradient(
      ellipse at center,
      transparent 20%,
      var(--background)
    );
  }
}

@utility bg-dots {
  & {
    position: absolute;
    inset: 0;

    background-size: 20px 20px;
    background-image: radial-gradient(var(--muted) 1px, transparent 1px);
    filter: brightness(1.5) sepia(1.5);

    mask-image: radial-gradient(circle, var(--background) 10%, transparent 90%);
    mask-repeat: no-repeat;
    mask-size: cover;
    mask-position: center;
  }
}

@utility typography {
  & :where(p) {
    @apply leading-7 [&:not(:first-child)]:mt-6;
  }

  & :where(h1) {
    @apply scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl;
  }

  & :where(h2) {
    @apply mt-10 scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight transition-colors first:mt-0;
  }

  & :where(h3) {
    @apply mt-8 scroll-m-20 text-2xl font-semibold tracking-tight;
  }

  & :where(h4) {
    @apply mt-6 scroll-m-20 text-xl font-semibold tracking-tight;
  }

  & :where(a) {
    @apply font-medium text-primary hover:text-primary/80 hover:underline hover:underline-offset-4;
  }

  & :where(blockquote) {
    @apply mt-6 border-l-2 pl-6 text-muted-foreground italic;
  }

  & :where(ul) {
    @apply my-6 ml-6 list-disc [&>li]:mt-2;
  }

  & :where(ol) {
    @apply my-6 ml-6 list-decimal [&>li]:mt-2;
  }

  & :where(code) {
    @apply relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm;
  }

  & :where(pre) {
    @apply mt-6 overflow-x-auto rounded-lg bg-muted p-4;

    & :where(code) {
      @apply bg-transparent p-0;
    }
  }

  & :where(figure) {
    @apply my-6 w-full overflow-y-auto;

    & :where(img) {
      @apply w-full rounded-lg;
    }

    & :where(figcaption) {
      @apply mt-2 text-center text-sm text-muted-foreground;
    }
  }

  & :where(hr) {
    @apply my-4 border-muted;
  }

  & :where(table) {
    @apply w-full overflow-y-auto;

    & :where(tr) {
      @apply m-0 border-t p-0 even:bg-muted;
    }

    & :where(thead) {
      & :where(th) {
        @apply border px-4 py-2 text-left font-bold [&[align=center]]:text-center [&[align=right]]:text-right;
      }
    }

    & :where(tbody) {
      & :where(td) {
        @apply border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right;
      }
    }
  }
}
