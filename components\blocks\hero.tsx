import { Section } from "@/components/layout/section"
import { Button } from "@/components/ui/button"
import { Background } from "@/components/layout/background"

const TITLE = {
  line1: 'Modern Software',
  line2: 'Future-Experiences',
  line3: 'Exceptional Design',
}

const DESCRIPTION1 =
  'We Craft Tailored Web, Mobile, And AI Solutions -'
const DESCRIPTION2 =
  'For Brands That Want To Lead, Not Follow.'

const Hero = () => {
  return (
      <Section container className="relative w-full min-h-screen items-start">
        <Background />
        {/* logo */}
        <div className="absolute w-[88px] h-[83.52px] top-[54.1px] left-0">
          <img src="/images/logo.svg" alt="Logo" className="w-full h-full object-contain" />
        </div>
        {/* text */}
        <div className="mt-50">
          <h1 className="text-color-brand-Color-3 text-6xl font-normal  uppercase leading-[93px] mb-5">
            {TITLE.line1}
          </h1>

          <div className="relative -ml-[40vw] left-1/2 flex items-center mb-5">
            <svg viewBox="0 0 200 20" className="w-1/2 h-10 fex-shrink-0">
              <path
                d="M0 10 Q50 5 100 10 T200 10"
                stroke="black"
                strokeWidth="3"
                fill="none"
              />
            </svg>
            <h1 className="text-color-brand-Color-3 text-6xl font-normal uppercase leading-[93px] whitespace-nowrap">
              {TITLE.line2}
            </h1>
          </div>

          <h1 className="text-color-brand-Color-3 text-6xl font-normal  uppercase leading-[93px] mb-15">
            {TITLE.line3}
          </h1>
          <p className="text-lg lg:text-xl mb-8">
            {DESCRIPTION1} <br /> {DESCRIPTION2}
          </p>
          {/* button */}
          <div className="flex items-center justify-between mt-20">
            {/* left button */}
            <div>
              <Button className="w-44 px-8 py-7 text-xl rounded-[0px_29.42px_0px_29.42px]  inline-flex justify-center items-center gap-2 ">
                CONTACT
                <img src="/images/Vector.svg" alt="" />
              </Button>
            </div>
            {/* right buttons */}
            <div className="absolute top-178 right-0 flex flex-col gap-8">
              <Button className="w-24 h-14 px-4 py-3 rounded-[29.42px_0_29.42px_0]">
                <img src="/images/icon-ai.svg" alt="" />
              </Button>

              <Button className="w-24 h-14 px-4 py-3 rounded-[0_29.42px_0_29.42px]">
                <img src="/images/menu.svg" alt="" />
              </Button>
            </div>
          </div>
        </div>
      </Section>
  )
}

export { Hero }
