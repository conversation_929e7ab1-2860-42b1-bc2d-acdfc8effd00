import { Section } from "@/components/layout/section"

export default function ServicesPage() {
  return (
    <Section container className="items-start pt-[140.17px]">
      {/* title */}
      <div>
        <div className="inline-flex items-center gap-4 mb-55 ml-10">
          <div className="flex items-end gap-2">
            <img src="/images/shape3.svg" alt="" className="w-20 h-20" />
            <div className="w-1 h-11 bg-black shadow-[2px_2px_3px_rgba(0,0,0,0.25)]" />
          </div>
          <h1 className="flex items-center text-5xl font-normal">
            OUR SERVICES
          </h1>
        </div>
      </div>

      {/* services */}
      <div>
        {/* application */}
        <div className="w-1440 h-[510.14px] relative bg-[url('/images/Rectangle-40047.svg')] bg-cover bg-center">
          {/* left */}
          <div className="w-96 h-80 left-[58.02px] top-[113.55px] absolute inline-flex flex-col justify-center items-center gap-12">
            <h1 className="text-white text-5xl font-normal uppercase leading-[66px]">application</h1>
            <div className="w-56 h-48">
              <img src="/images/shape.svg" alt="" />
            </div>
          </div>

          {/* separator */}
          <div className="w-0 h-96 left-[495.02px] top-[67.36px] absolute outline-offset-[-1.5px] outline-white" />

            {/* texts */}
            <div>
              <h1 className="left-[535.80px] top-[108.05px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
                iOS & Android
              </h1>
              <p className="w-80 h-16 left-[535.80px] top-[150px] absolute text-white text-xl font-light capitalize leading-[1.5]">
                High-performance apps using Swift, Kotlin, and Jetpack Compose
              </p>
            </div>

            <div>
              <h1 className="left-[930px] top-[108.05px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
                Cross-Platform
              </h1>
              <p className="w-80 h-16 left-[930px] top-[150px] absolute text-white text-xl font-light capitalize leading-[1.5]">
                Single codebase solutions with Flutter or React Native
              </p>
            </div>

            <div>
              <h1 className="left-[535.80px] top-[294.57px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
                UI/UX Focused
              </h1>
              <p className="w-80 h-16 left-[535.80px] top-[335px] absolute text-white text-xl font-light capitalize leading-[1.5]">
                Intuitive interfaces following platform-specific design guidelines
              </p>
            </div>

            <div>
              <h1 className="left-[930px] top-[294.57px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
                App Store
              </h1>
              <p className="w-80 h-16 left-[930px] top-[335px] absolute text-white text-xl font-light capitalize leading-[1.5]">
                Intuitive interfaces following platform-specific design guidelines
              </p>
            </div>
        </div>

        {/* website */}
        <div className="w-1440 h-[510.14px] relative bg-[url('/images/Rectangle-40047.svg')] bg-cover bg-center mt-25">
          {/* left */}
          <div className="w-96 h-80 left-[58.02px] top-[113.55px] absolute inline-flex flex-col justify-center items-center gap-12">
            <h1 className="text-white text-5xl font-normal uppercase leading-[66px]">website</h1>
            <div className="w-56 h-48">
              <img src="/images/shape1.svg" alt="" />
            </div>
          </div>

          {/* separator */}
          <div className="w-0 h-96 left-[495.02px] top-[67.36px] absolute outline-offset-[-1.5px] outline-white" />

          {/* texts */}
          <div>
            <h1 className="left-[535.80px] top-[108.05px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
              Responsive
            </h1>
            <p className="w-80 h-16 left-[535.80px] top-[150px] absolute text-white text-xl font-light capitalize leading-[1.5]">
              High-performance apps using Swift, Kotlin, and Jetpack Compose
            </p>
          </div>

          <div>
            <h1 className="left-[930px] top-[108.05px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
              SEO optimized
            </h1>
            <p className="w-80 h-16 left-[930px] top-[150px] absolute text-white text-xl font-light capitalize leading-[1.5]">
              Single codebase solutions with Flutter or React Native
            </p>
          </div>

          <div>
            <h1 className="left-[535.80px] top-[294.57px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
              Fast loading
            </h1>
            <p className="w-80 h-16 left-[535.80px] top-[335px] absolute text-white text-xl font-light capitalize leading-[1.5]">
              Intuitive interfaces following platform-specific design guidelines
            </p>
          </div>

          <div>
            <h1 className="left-[930px] top-[294.57px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
              E-commerce
            </h1>
            <p className="w-80 h-16 left-[930px] top-[335px] absolute text-white text-xl font-light capitalize leading-[1.5]">
              Intuitive interfaces following platform-specific design guidelines
            </p>
          </div>
        </div>

        {/* ai solution */}
        <div className="w-1440 h-[510.14px] relative bg-[url('/images/Rectangle-40047.svg')] bg-cover bg-center mt-25">
          {/* left */}
          <div className="w-96 h-80 left-[58.02px] top-[113.55px] absolute inline-flex flex-col justify-center items-center gap-12">
            <h1 className="text-white text-5xl font-normal uppercase leading-[66px]">ai solution</h1>
            <div className="w-56 h-48">
              <img src="/images/shape2.svg" alt="" />
            </div>
          </div>

          {/* separator */}
          <div className="w-0 h-96 left-[495.02px] top-[67.36px] absolute outline-offset-[-1.5px] outline-white" />

          {/* texts - same as website */}  
          <div>
            <h1 className="left-[535.80px] top-[108.05px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
              Responsive
            </h1>
            <p className="w-80 h-16 left-[535.80px] top-[150px] absolute text-white text-xl font-light capitalize leading-[1.5]">
              High-performance apps using Swift, Kotlin, and Jetpack Compose
            </p>
          </div>

          <div>
            <h1 className="left-[930px] top-[108.05px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
              SEO optimized
            </h1>
            <p className="w-80 h-16 left-[930px] top-[150px] absolute text-white text-xl font-light capitalize leading-[1.5]">
              Single codebase solutions with Flutter or React Native
            </p>
          </div>

          <div>
            <h1 className="left-[535.80px] top-[294.57px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
              Fast loading
            </h1>
            <p className="w-80 h-16 left-[535.80px] top-[335px] absolute text-white text-xl font-light capitalize leading-[1.5]">
              Intuitive interfaces following platform-specific design guidelines
            </p>
          </div>

          <div>
            <h1 className="left-[930px] top-[294.57px] absolute text-white text-[34px] font-light uppercase leading-[51px]">
              E-commerce
            </h1>
            <p className="w-80 h-16 left-[930px] top-[335px] absolute text-white text-xl font-light capitalize leading-[1.5]">
              Intuitive interfaces following platform-specific design guidelines
            </p>
          </div>
        </div>
      </div>

      {/* help section */}
      <div>
        <div>
          <div className="inline-flex items-center gap-4 ml-10 mt-55">
            <div className="flex items-end gap-2">
              <div className="w-1 h-14 bg-black shadow-[2px_2px_3px_rgba(0,0,0,0.25)]" />
              <div className="w-1 h-11 bg-black shadow-[2px_2px_3px_rgba(0,0,0,0.25)]" />
            </div>
            <h1 className="flex items-center text-5xl font-normal text-color-brand-Color-3 uppercase leading-[72px]">
              how can we <img src="/images/shape3.svg" alt="" className="w-20 h-20" /> help
            </h1>
          </div>
        </div>

        {/* service cards */}
        <div className="mt-40">
          <div className="flex justify-start items-center gap-50">
            <div className="w-72 h-80 inline-flex flex-col justify-start items-center gap-10 ml-5">
              <img src="/images/shape4.svg" alt="" className="w-56 h-48" />
              <h1 className="text-center text-3xl font-normal uppercase leading-[48px]">
                Update your platform
              </h1>
            </div>

            <div className="w-80 h-96 inline-flex flex-col justify-start items-center gap-10 mt-3">
              <img src="/images/shape5.svg" alt="" className="w-56 h-56" />
              <h1 className="text-center text-3xl font-normal uppercase leading-[48px]">
                Simplify user journeys
              </h1>
            </div>

            <div className="w-80 h-96 inline-flex flex-col justify-start items-center gap-10 mt-5 mr-10">
              <img src="/images/shape6.svg" alt="" className="w-56 h-56" />
              <h1 className="text-center text-3xl font-normal uppercase leading-[48px]">
                Get higher conversions
              </h1>
            </div>
          </div>
        </div>
      </div>
    </Section>
  )
}
