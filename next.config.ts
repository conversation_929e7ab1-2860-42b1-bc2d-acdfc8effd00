import { withPayload } from "@payloadcms/next/withPayload";
import createMD<PERSON> from "@next/mdx"
import type { NextConfig } from "next"
import withPlugins from "next-compose-plugins"

const nextConfig: NextConfig = {
  experimental: {
    // typedRoutes: true,
    viewTransition: true,
    mdxRs: true
  },

  pageExtensions: ["js", "jsx", "md", "mdx", "ts", "tsx"],

  poweredByHeader: false,

  images: {
    formats: ["image/webp", "image/avif"],
    minimumCacheTTL: 60,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        pathname: "/**",
        port: "",
        search: ""
      }
    ]
  },

  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY"
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff"
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin"
          },
          {
            key: "X-DNS-Prefetch-Control",
            value: "on"
          },
          {
            key: "Strict-Transport-Security",
            value: "max-age=31536000; includeSubDomains"
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block"
          },
          {
            key: "Permissions-Policy",
            value: "camera=(), microphone=(), geolocation=()"
          }
        ]
      }
    ]
  }
}

const withMDX = createMDX({
  extension: /\.mdx?$/,
  options: {
    remarkPlugins: [],
    rehypePlugins: []
  }
})

const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true"
})

export default withPayload(withPlugins([withMDX, withBundleAnalyzer], nextConfig))
