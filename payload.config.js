const path = require('path')
const { mongooseAdapter } = require('@payloadcms/db-mongodb')
const { payloadCloudPlugin } = require('@payloadcms/payload-cloud')
const { lexicalEditor } = require('@payloadcms/richtext-lexical')
const { buildConfig } = require('payload')
const sharp = require('sharp')

const { Users } = require('./collections/Users.js')
const { Media } = require('./collections/Media.js')
const { Categories } = require('./collections/Categories.js')

module.exports = buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(__dirname),
    },
  },
  collections: [Users, Media, Categories],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(__dirname, 'payload-types.ts'),
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || '',
  }),
  sharp,
  plugins: [
    payloadCloudPlugin(),
  ],
})
